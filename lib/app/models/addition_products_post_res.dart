import 'res_base.dart';
import 'package:dio/dio.dart';

class AdditionProductsPostRes extends ResBase {
  bool isCreated;
  int additionProductId;

  AdditionProductsPostRes({this.isCreated, this.additionProductId});

  AdditionProductsPostRes.resError(Response response) : super.resError(response);
  AdditionProductsPostRes.unKnownError() : super.unKnownError();

  AdditionProductsPostRes.fromJson(Map<String, dynamic> json) : super.fromErrorJson(json) {
    isCreated = json['is_created'];
    additionProductId = json['addition_product_id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['is_created'] = this.isCreated;
    data['addition_product_id'] = this.additionProductId;
    return data;
  }
}