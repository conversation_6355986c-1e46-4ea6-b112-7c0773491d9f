import 'res_base.dart';
import 'package:dio/dio.dart';

class CategoriesPutRes extends ResBase {
  bool isUpdated;
  int categoryId;

  CategoriesPutRes({this.isUpdated, this.categoryId});

  CategoriesPutRes.resError(Response response) : super.resError(response);
  CategoriesPutRes.unKnownError() : super.unKnownError();

  CategoriesPutRes.fromJson(Map<String, dynamic> json) : super.fromErrorJson(json) {
    isUpdated = json['is_updated'];
    categoryId = json['category_id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['is_updated'] = this.isUpdated;
    data['category_id'] = this.categoryId;
    return data;
  }
}