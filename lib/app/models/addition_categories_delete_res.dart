import 'res_base.dart';
import 'package:dio/dio.dart';

class AdditionCategoriesDeleteRes extends ResBase {
  bool isDeleted;

  AdditionCategoriesDeleteRes({this.isDeleted});

  AdditionCategoriesDeleteRes.resError(Response response) : super.resError(response);
  AdditionCategoriesDeleteRes.unKnownError() : super.unKnownError();

  AdditionCategoriesDeleteRes.fromJson(Map<String, dynamic> json) : super.fromErrorJson(json) {
    isDeleted = json['is_deleted'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['is_deleted'] = this.isDeleted;
    return data;
  }
}