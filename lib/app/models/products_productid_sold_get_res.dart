import 'res_base.dart';
import 'package:dio/dio.dart';

class ProductsProductIdSoldGetRes extends ResBase {
  bool isUpdated;
  int productId;

  ProductsProductIdSoldGetRes({this.isUpdated, this.productId});

  ProductsProductIdSoldGetRes.resError(Response response) : super.resError(response);
  ProductsProductIdSoldGetRes.unKnownError() : super.unKnownError();

  ProductsProductIdSoldGetRes.fromJson(Map<String, dynamic> json) {
    isUpdated = json['is_updated'];
    productId = json['product_id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['is_updated'] = this.isUpdated;
    data['product_id'] = this.productId;
    return data;
  }
}