import 'res_base.dart';
import 'package:dio/dio.dart';

class CategoriesDeleteRes extends ResBase {
  bool isDeleted;

  CategoriesDeleteRes({this.isDeleted});

  CategoriesDeleteRes.resError(Response response) : super.resError(response);
  CategoriesDeleteRes.unKnownError() : super.unKnownError();

  CategoriesDeleteRes.fromJson(Map<String, dynamic> json) : super.fromErrorJson(json) {
    isDeleted = json['is_deleted'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['is_deleted'] = this.isDeleted;
    return data;
  }
}