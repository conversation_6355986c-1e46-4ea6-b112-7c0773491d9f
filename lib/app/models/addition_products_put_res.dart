import 'res_base.dart';
import 'package:dio/dio.dart';

class AdditionProductsPutRes extends ResBase {
  bool isUpdated;
  int additionProductId;

  AdditionProductsPutRes({this.isUpdated, this.additionProductId});

  AdditionProductsPutRes.resError(Response response) : super.resError(response);
  AdditionProductsPutRes.unKnownError() : super.unKnownError();

  AdditionProductsPutRes.fromJson(Map<String, dynamic> json) : super.fromErrorJson(json) {
    isUpdated = json['is_updated'];
    additionProductId = json['addition_product_id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['is_updated'] = this.isUpdated;
    data['addition_product_id'] = this.additionProductId;
    return data;
  }
}