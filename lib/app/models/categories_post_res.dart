import 'res_base.dart';
import 'package:dio/dio.dart';

class CategoriesPostRes extends ResBase {
  bool isCreated;
  int categoryId;

  CategoriesPostRes({this.isCreated, this.categoryId});

  CategoriesPostRes.resError(Response response) : super.resError(response);
  CategoriesPostRes.unKnownError() : super.unKnownError();

  CategoriesPostRes.fromJson(Map<String, dynamic> json) : super.fromErrorJson(json) {
    isCreated = json['is_created'];
    categoryId = json['category_id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['is_created'] = this.isCreated;
    data['category_id'] = this.categoryId;
    return data;
  }
}